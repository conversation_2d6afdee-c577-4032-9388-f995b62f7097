{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}, {"src": "api/index.js", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/api/index.js"}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "dest": "/dist/$1"}, {"src": "/(.*)", "dest": "/dist/index.html"}], "functions": {"api/index.js": {"maxDuration": 10}}, "env": {"NODE_ENV": "production"}, "regions": ["fra1"]}